import { useState, useEffect } from 'react';
import { useToast } from '@/components/ui/use-toast';
import PageHeader from '@/components/common/PageHeader';
import { Card, CardContent, CardDescription, CardHeader, CardTitle, CardFooter } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { BookOpen, FileText, Plus, Search, BookMarked, Clock, Download, FolderOpen, Target, Building } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import apiService from '@/services/apiService';

// Types for the curate tab
interface KnowledgeArea {
  id: string;
  name: string;
  description?: string;
}

interface Topic {
  id: string;
  name: string;
  areaId: string;
  description?: string;
}

interface Unit {
  id: string;
  name: string;
  topicId: string;
  description?: string;
}

const KnowledgePage = () => {
  const { toast } = useToast();
  const [searchQuery, setSearchQuery] = useState('');

  // Curate tab state
  const [knowledgeAreas, setKnowledgeAreas] = useState<KnowledgeArea[]>([]);
  const [topics, setTopics] = useState<Topic[]>([]);
  const [units, setUnits] = useState<Unit[]>([]);
  const [selectedArea, setSelectedArea] = useState<string>('');
  const [selectedTopic, setSelectedTopic] = useState<string>('');
  const [loading, setLoading] = useState(false);

  // Fetch knowledge areas on component mount
  useEffect(() => {
    fetchKnowledgeAreas();
  }, []);

  // Fetch topics when area is selected
  useEffect(() => {
    if (selectedArea) {
      fetchTopics(selectedArea);
      setSelectedTopic(''); // Reset topic selection
      setUnits([]); // Clear units
    }
  }, [selectedArea]);

  // Fetch units when topic is selected
  useEffect(() => {
    if (selectedTopic) {
      fetchUnits(selectedTopic);
    }
  }, [selectedTopic]);

  const fetchKnowledgeAreas = async () => {
    try {
      setLoading(true);
      const response = await apiService.get('/knowledge-areas');
      setKnowledgeAreas(response);
    } catch (error) {
      console.error('Error fetching knowledge areas:', error);
      toast({
        title: "Error",
        description: "Failed to fetch knowledge areas",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchTopics = async (areaId: string) => {
    try {
      setLoading(true);
      // Assuming there's an endpoint for topics by area
      const response = await apiService.get(`/knowledge-areas/${areaId}/topics`);
      setTopics(response);
    } catch (error) {
      console.error('Error fetching topics:', error);
      toast({
        title: "Error",
        description: "Failed to fetch topics",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const fetchUnits = async (topicId: string) => {
    try {
      setLoading(true);
      // Assuming there's an endpoint for units by topic
      const response = await apiService.get(`/topics/${topicId}/units`);
      setUnits(response);
    } catch (error) {
      console.error('Error fetching units:', error);
      toast({
        title: "Error",
        description: "Failed to fetch units",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <PageHeader
          title="Knowledge"
          description="Organize and deliver structured training and knowledge resources across the enterprise"
        />
        <Button className="flex items-center gap-1" onClick={() => 
          toast({ 
            title: "Add Resource", 
            description: "Resource creation functionality will be implemented soon." 
          })
        }>
          <Plus className="h-4 w-4" /> Add Resource
        </Button>
      </div>

      <Tabs defaultValue="library" className="w-full">
        <TabsList className="grid w-full md:w-[800px] grid-cols-4">
          <TabsTrigger value="library">Knowledge Library</TabsTrigger>
          <TabsTrigger value="curate">Curate</TabsTrigger>
          <TabsTrigger value="my-training">My Training</TabsTrigger>
          <TabsTrigger value="team-learning">Team Learning</TabsTrigger>
        </TabsList>
        
        <TabsContent value="library" className="space-y-4">
          <div className="flex items-center justify-between gap-4 mb-4">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
              <Input
                placeholder="Search knowledge resources..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Safety Procedures</CardTitle>
                  <BookMarked className="h-5 w-5 text-blue-500" />
                </div>
                <CardDescription>Standard Operating Procedures</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Comprehensive guide to safety procedures for all operational activities.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Safety</Badge>
                  <Badge variant="outline">Procedures</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Equipment Training</CardTitle>
                  <BookMarked className="h-5 w-5 text-green-500" />
                </div>
                <CardDescription>Operational Training</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Training materials for safe operation of all facility equipment.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Training</Badge>
                  <Badge variant="outline">Equipment</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <CardTitle className="text-lg">Regulatory Compliance</CardTitle>
                  <BookMarked className="h-5 w-5 text-amber-500" />
                </div>
                <CardDescription>Legal & Compliance</CardDescription>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground mb-4">
                  Documentation on regulatory requirements and compliance procedures.
                </p>
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Badge variant="outline">Compliance</Badge>
                  <Badge variant="outline">Regulatory</Badge>
                </div>
              </CardContent>
              <CardFooter className="pt-0">
                <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                  <FileText className="h-3 w-3" /> View Documents
                </Button>
              </CardFooter>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="curate" className="space-y-4">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Area Column */}
            <Card className="h-fit">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <FolderOpen className="h-5 w-5 text-blue-500" />
                  <CardTitle className="text-lg">Knowledge Areas</CardTitle>
                </div>
                <CardDescription>Select a knowledge area to explore topics</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {loading && knowledgeAreas.length === 0 ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-blue-500 mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">Loading areas...</p>
                  </div>
                ) : knowledgeAreas.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No knowledge areas available</p>
                  </div>
                ) : (
                  knowledgeAreas.map((area) => (
                    <div
                      key={area.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                        selectedArea === area.id
                          ? 'border-blue-500 bg-blue-50 shadow-sm'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedArea(area.id)}
                    >
                      <h4 className="font-medium text-sm">{area.name}</h4>
                      {area.description && (
                        <p className="text-xs text-muted-foreground mt-1">{area.description}</p>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Topic Column */}
            <Card className="h-fit">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <Target className="h-5 w-5 text-green-500" />
                  <CardTitle className="text-lg">Topics</CardTitle>
                </div>
                <CardDescription>
                  {selectedArea ? 'Select a topic to view units' : 'Choose an area first'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {!selectedArea ? (
                  <div className="text-center py-8">
                    <Target className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Select a knowledge area to view topics</p>
                  </div>
                ) : loading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-green-500 mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">Loading topics...</p>
                  </div>
                ) : topics.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No topics available for this area</p>
                  </div>
                ) : (
                  topics.map((topic) => (
                    <div
                      key={topic.id}
                      className={`p-3 rounded-lg border cursor-pointer transition-all hover:shadow-md ${
                        selectedTopic === topic.id
                          ? 'border-green-500 bg-green-50 shadow-sm'
                          : 'border-gray-200 hover:border-gray-300'
                      }`}
                      onClick={() => setSelectedTopic(topic.id)}
                    >
                      <h4 className="font-medium text-sm">{topic.name}</h4>
                      {topic.description && (
                        <p className="text-xs text-muted-foreground mt-1">{topic.description}</p>
                      )}
                    </div>
                  ))
                )}
              </CardContent>
            </Card>

            {/* Unit Column */}
            <Card className="h-fit">
              <CardHeader className="pb-4">
                <div className="flex items-center gap-2">
                  <Building className="h-5 w-5 text-purple-500" />
                  <CardTitle className="text-lg">Units</CardTitle>
                </div>
                <CardDescription>
                  {selectedTopic ? 'Learning units for this topic' : 'Choose a topic first'}
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {!selectedTopic ? (
                  <div className="text-center py-8">
                    <Building className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
                    <p className="text-sm text-muted-foreground">Select a topic to view units</p>
                  </div>
                ) : loading ? (
                  <div className="text-center py-4">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-purple-500 mx-auto"></div>
                    <p className="text-sm text-muted-foreground mt-2">Loading units...</p>
                  </div>
                ) : units.length === 0 ? (
                  <div className="text-center py-4">
                    <p className="text-sm text-muted-foreground">No units available for this topic</p>
                  </div>
                ) : (
                  units.map((unit) => (
                    <div
                      key={unit.id}
                      className="p-3 rounded-lg border border-gray-200 hover:border-gray-300 hover:shadow-md transition-all cursor-pointer"
                    >
                      <h4 className="font-medium text-sm">{unit.name}</h4>
                      {unit.description && (
                        <p className="text-xs text-muted-foreground mt-1">{unit.description}</p>
                      )}
                      <div className="mt-2 flex gap-2">
                        <Button variant="outline" size="sm" className="text-xs h-7">
                          <FileText className="h-3 w-3 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm" className="text-xs h-7">
                          <Download className="h-3 w-3 mr-1" />
                          Download
                        </Button>
                      </div>
                    </div>
                  ))
                )}
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="my-training" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">Annual Safety Refresher</CardTitle>
                    <CardDescription>Required Training</CardDescription>
                  </div>
                  <Badge className="bg-red-500">Due Soon</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Due in 5 days</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Estimated time: 45 minutes</span>
                  </div>
                  <div className="pt-2">
                    <Button variant="default" size="sm" className="w-full flex items-center justify-center gap-1">
                      <BookOpen className="h-3 w-3" /> Start Training
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <div className="flex justify-between items-start">
                  <div>
                    <CardTitle className="text-lg">New Equipment Orientation</CardTitle>
                    <CardDescription>Optional Training</CardDescription>
                  </div>
                  <Badge className="bg-blue-500">New</Badge>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Available until June 30, 2023</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <BookOpen className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">Estimated time: 30 minutes</span>
                  </div>
                  <div className="pt-2">
                    <Button variant="outline" size="sm" className="w-full flex items-center justify-center gap-1">
                      <BookOpen className="h-3 w-3" /> View Details
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>
        
        <TabsContent value="team-learning" className="space-y-4">
          <div className="bg-card rounded-lg shadow-sm p-6">
            <h2 className="text-xl font-semibold mb-4">Team Learning</h2>
            <p className="text-muted-foreground">
              This tab will display team learning progress and assigned training materials.
            </p>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default KnowledgePage;
